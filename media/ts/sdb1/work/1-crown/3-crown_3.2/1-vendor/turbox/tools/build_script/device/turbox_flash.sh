#!/bin/bash
# Author: <PERSON><PERSON><PERSON> <<EMAIL>>
# Date: 2020-01-22
# Copyright (c) 2020, Thundercomm All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
#       copyright notice, this list of conditions and the following
#       disclaimer in the documentation and/or other materials provided
#       with the distribution.
#     * Neither the name of The Linux Foundation nor the names of its
#       contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
# ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
# BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
# BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
# WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
# OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
# IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

usage() {
    echo -e "\033[1;37mUsage:\033[0m"
    echo -e "  bash $0 [options] [options]"
    echo -e "\033[1;37m  Images except gpt,userdata,persist will be flashed if no options input\033[0m"
    echo

    echo -e "\033[1;37mDescription:\033[0m"
    echo -e "  flash images with fasboot"
    echo

    echo -e "\033[1;37mOptions:\033[0m"
    echo -e "\033[1;37m  -h, --help\033[0m   display this help message"
    echo -e "\033[1;37m  -v, --variant <debug/perf/user>\033[0m    set the build variant.(default: debug)"

    echo -e "\033[1;37m  -f, --force\033[0m  forcibly flash gpt,persist and userdata without confirmation."
    echo -e "\033[31m               User data and calibration data(BT/WIFI MAC address, etc..) will be ereased. \033[0m"
    echo -e "\033[1;37m  -r, --reboot\033[0m      reboot the device automatically after flashing completes."
    echo -e "\033[1;37m  -b, --nonhlos\033[0m     flash all bp images(xbl,rpm,ftfm,tz,dsp,modem,pmic)."
    echo -e "\033[1;37m  -a, --hlos\033[0m        flash all ap images(aboot,bootimage,recovery,system,vendor,cache,mdtp,userdata,persist)."
    echo -e "\033[1;37m  --all\033[0m        flash all images"
    echo

    echo -e "\033[1;37m  -l, --fh_loader\033[0m                flash all images using fh_loader"
    echo -e "\033[1;37m  -p, specify the product name(c6490/ct6490), default:c6490"
    echo -e "\033[1;37m  -m, --memoryname <eMMC|ufs>\033[0m    specify the memory type"
    echo

    echo -e "\033[1;37mExample:\033[0m"
    echo -e "\033[1;37m  ./turbox_flash.sh\033[0m"
    echo -e "      Flash the images except gpt,userdata,persist."
    echo
    echo -e "\033[1;37m  ./turbox_flash.sh --force\033[0m"
    echo -e "      Flash all images."
    echo
    echo -e "\033[1;37m  ./turbox_flash.sh --hlos\033[0m"
    echo -e "      Flash all ap images."
    echo
}

assert() {
    if [ $? -ne 0 ];then
        echo "`date +%Y%m%d_%H:%M:%S` [TURBOX BUILD ERROR]: $1 Error****" | tee -a $log_file
        exit 1;
    fi
}

# ==================================================================
flash_gpt() {
    local select
    local execution

    if [ "$FORCE" = "" ]; then
        echo -e "\033[31m User data and the calibration data(BT/WIFI MAC address, etc..) will be ereased. Are you sure to flash the gpt.(Y/yes)? \033[0m"

        read select

        case $select in
            yes|Yes|Y|y)   execution="true";;
            *)             execution="false";;
        esac

    elif [ "$FORCE" = "true" ]; then
        execution="true"
    else
        execution="false"
    fi

    if [ "$execution" = "true" ]; then
        fastboot flash partition:0 QCM2290.LA.1.0.1/common/build/emmc/gpt_both0.bin
    fi
}

flash_persist() {
    local select
    local execution

    if [ "$FORCE" = "" ]; then
        echo -e "\033[31m Calibration data(BT/WIFI MAC address, etc..) will be ereased. Are you sure to flash the persist image(Y/yes)? \033[0m"

        read select

        case $select in
            yes|Yes|Y|y)   execution="true";;
            *)             execution="false";;
        esac

    elif [ "$FORCE" = "true" ]; then
        execution="true"
    else
        execution="false"
    fi

    if [ "$execution" = "true" ]; then
        fastboot flash persist LINUX/android/out/target/product/lahaina/persist.img
    fi
}

flash_userdata() {
    local select
    local execution

    if [ "$FORCE" = "" ]; then
        echo -e "\033[31m User data will be ereased. Are you sure to flash the userdata image(Y/yes)? \033[0m"

        read select

        case $select in
            yes|Yes|Y|y)   execution="true";;
            *)             execution="false";;
        esac

    elif [ "$FORCE" = "true" ]; then
        execution="true"
    else
        execution="false"
    fi

    if [ "$execution" = "true" ]; then
        fastboot flash userdata LINUX/android/out/target/product/lahaina/userdata.img
    fi
}

flash_hlos() {
    fastboot flash abl_a LINUX/android/out/target/product/lahaina/abl.elf
    fastboot flash boot_a LINUX/android/out/target/product/lahaina/boot.img
    fastboot flash dtbo_a LINUX/android/out/target/product/lahaina/dtbo.img
    fastboot flash metadata LINUX/android/out/target/product/lahaina/metadata.img
    fastboot flash super LINUX/android/out/target/product/lahaina/super.img
    fastboot flash vbmeta_a LINUX/android/out/target/product/lahaina/vbmeta.img
    fastboot flash vbmeta_system_a LINUX/android/out/target/product/lahaina/vbmeta_system.img
    fastboot flash vendor_boot_a LINUX/android/out/target/product/lahaina/vendor_boot.img
}

flash_nonhlos() {
    fastboot flash modem_a common/build/ufs/bin/asic/NON-HLOS.bin
    fastboot flash bluetooth_a common/build/ufs/bin/BTFM.bin
    fastboot flash dsp_a common/build/bin/dspso.bin
    fastboot flash splash common/build/bin/splash.img
    fastboot flash qupfw_a common/core_qupv3fw/kodiak/qupv3fw.elf
    fastboot flash devcfg_a trustzone_images/build/ms/bin/IAGAANAA/devcfg.mbn
    fastboot flash hyp_a trustzone_images/build/ms/bin/IAGAANAA/hypvm.mbn
    fastboot flash keymaster_a qtee_tas/build/ms/bin/IAGAANAA/km41.mbn
    fastboot flash featenabler_a qtee_tas/build/ms/bin/IAGAANAA/featenabler.mbn
    fastboot flash storsec qtee_tas/build/ms/bin/IAGAANAA/storsec.mbn
    fastboot flash uefisecapp_a qtee_tas/build/ms/bin/IAGAANAA/uefi_sec.mbn
    fastboot flash imagefv_a boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/imagefv.elf
    fastboot flash logfs boot_images/boot/QcomPkg/Tools/binaries/logfs_ufs_8mb.bin
    fastboot flash tz_a trustzone_images/build/ms/bin/IAGAANAA/tz.mbn
    fastboot flash xbl_a boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/xbl.elf
    fastboot flash xbl_config_a boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/xbl_config.elf

    fastboot flash rtice trustzone_images/build/ms/bin/IAGAANAA/rtice.mbn
    fastboot flash shrm_a boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/shrm.elf
    fastboot flash secdata common/sectools/resources/build/sec.dat
    fastboot flash aop_a aop_proc/build/ms/bin/AAAAANAZO/kodiak/aop.mbn

    if [ "$PRODUCT" = "c6490" ]; then
        fastboot flash cdt boot_images/boot/QcomPkg/Tools/cdt_c6490.bin
    elif [ "$PRODUCT" = "ct6490" ]; then
        fastboot flash cdt boot_images/boot/QcomPkg/Tools/cdt_ct6490.bin
    else
        fastboot flash cdt boot_images/boot/QcomPkg/Tools/cdt_c6490.bin
    fi
}
# ======================Setup getopt================================
long_opts="help,force,reboot,all,nonhlos,hlos,variant,fh_loader,dir,product,memoryname:"
getopt_cmd=$(getopt -o hfrabv:ld:p:m: --long "$long_opts" \
           -n $(basename $0) -- "$@") || \
           { echo -e "\nERROR: Getopt failed. Extra args\n"; usage; exit 1;}

eval set -- "$getopt_cmd"

VARIANT="debug"

while true; do
    case "$1" in
        -h|--help)       usage; exit 0;;
        -f|--force)      FORCE="true";;
        -r|--reboot)     REBOOT="true";;
        -v|--variant)    VARIANT=$2;shift;;
        -a|--hlos)       HLOS="true";OPTS="true";;
        -b|--nonhlos)    NONHLOS="true";OPTS="true";;
        -l|--fh_loader)  FH_LOADER="true";;
        -p|--product)    PRODUCT="$2";shift;;
        -m|--memoryname) MEM="$2";shift;;
        --all)           ALL="true";OPTS="true";;
        --)              break;;
    esac
    shift

    if [ "$1" = "" ]; then
        break
    fi
done

if [ "$FH_LOADER" = "true" ]; then
    #flash_fh_loader
    exit
fi

if [ "$OPTS" != "true" ]; then
   FORCE="false"
   flash_nonhlos
   flash_hlos

fi

if [ "$ALL" = "true" ]; then
   #flash_gpt
   flash_nonhlos
   flash_hlos
   flash_persist
   flash_userdata
else
    if [ "$HLOS" = "true" ]; then
       flash_hlos
       flash_persist
       flash_userdata
    fi
    if [ "$NONHLOS" = "true" ]; then
        flash_nonhlos
    fi
fi

if [ "$REBOOT" = "true" ]; then
    echo "Loading complete, Device rebooting ..."
    fastboot reboot
    sleep 5
else
    echo "Loading complete, please reboot the device"
fi
