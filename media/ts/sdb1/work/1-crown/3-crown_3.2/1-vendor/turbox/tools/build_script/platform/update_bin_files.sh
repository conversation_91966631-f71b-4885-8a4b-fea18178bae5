#!/bin/bash
# Author: <PERSON> <<EMAIL>>
# Date: 2020-01-16
# Copyright (c) 2020, Thundercomm All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
#       copyright notice, this list of conditions and the following
#       disclaimer in the documentation and/or other materials provided
#       with the distribution.
#     * Neither the name of The Linux Foundation nor the names of its
#       contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
# ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
# BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
# BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
# WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
# OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
# IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

LOGFILE=update_bin_files.log

usage() {
cat <<USAGE

Usage:
    bash $0 [OPTIONS] [OPTIONS]

Description:
    update nonhlos bin files.

OPTIONS:
    -l, --log    Put build log to file
    -h, --help   Display this help message
    --all        Update all bin files
    --common     Update common bin files
    --btfm       Update btfm_proc bin files
    --btfm-cmc   Update btfm_proc_cmc bin files
    --adsp       Update adsp_proc bin files
    --cdsp       Update cdsp_proc bin files
    --mpss       Update mpss_proc bin files
    --slpi       Update slpi_proc bin files
    --bl         Update boot_images bin files
    --rpm        Update rpm_proc bin files
    --aop        Update aop_proc bin files
    --tz         Update trustzone_proc bin files
    --venus      Update venus_proc bin files
    --wdsp       Update wdsp_proc bin files
    --wlan       Update wlan_proc bin files
    --qtee       Update qtee_tas bin files

Example:
    build all bin files:
        $0 -a

    build modem_proc and save the log:
        $0 -m -l

USAGE
}

STORAGE='ufs'
FLAVOR='asic'
BUILD=''

# ==================================================================
assert() {
    if [ $? -ne 0 ]; then
        exit 1
    fi
}

update_bin_files() {
    local filelist=$2
    local destdir=""

    if [[ "$1" == "common" ]]; then
        filelist="common/*"
    fi

    if [[ -d "$meta_dir/BP-CODE" ]]; then
        destdir=$meta_dir/$1
    else
        destdir=`dirname $meta_dir`/$1
    fi

    if [ ! -d "$destdir" ]; then
        echo "Error! No dest dir: $destdir" 2>&1 | tee -a $log_file
        exit 1
    fi

    echo "" 2>&1 | tee -a $log_file
    echo "==Update $1 file==" 2>&1 | tee -a $log_file
    echo "" 2>&1 | tee -a $log_file

    for file in $filelist; do
        if [[ -d "$meta_dir/BP-CODE" ]]; then
            # out of BP-CODE
            srcfile=`echo $file | sed "s/$1/BP-CODE\/$1/"`
            destfile=$file
        else
            srcfile=$file
            destfile=`echo $file | sed "s/BP-CODE\///"`
        fi

        if [[ ! -d `dirname $destfile` ]]; then
            mkdir -p `dirname $destfile`
        fi

        echo "cp -frp $srcfile $destfile" 2>&1 | tee -a $log_file
        cp -frp $srcfile $destfile
        assert
    done
}

# ======================Setup getopt================================
while true; do
    case "$1" in
        -l|--log)      BUILD_LOG="true";;
        -h|--help)     usage; exit 0;;
        -p|--product)  PRODUCT=$2;;
        -s|--storage)  STORAGE=$2;;
        -f|--flavor)   FLAVOR=$2;;
        --all)         ALL="true";OPTS="true";;
        --common)      BUILD+="common ";OPTS="true";;   # build name: "common"
        --aop)         BUILD+="aop ";OPTS="true";;      # build name: "aop"
        --btfm)        BUILD+="btfm ";OPTS="true";;     # build name: "btfm"
        --btfm-cmc)    BUILD+="btfm_hsp ";OPTS="true";; # build name: "btfm_hsp"
        --adsp)        BUILD+="adsp ";OPTS="true";;     # build name: "adsp"
        --bl)          BUILD+="boot ";OPTS="true";;       # build name: "boot"
        --cdsp)        BUILD+="cdsp ";OPTS="true";;     # build name: "cdsp"
        --mpss)        BUILD+="modem ";OPTS="true";;     # build name: "modem"
        --slpi)        BUILD+="slpi ";OPTS="true";;     # build name: "slpi"
        --rpm)         BUILD+="rpm ";OPTS="true";;      # build name: "rpm"
        --qtee)        BUILD+="tz_apps ";OPTS="true";;     # build name: "tz_apps"
        --tz)          BUILD+="tz ";OPTS="true";;       # build name: "tz"
        --venus)       BUILD+="venus ";OPTS="true";;    # build name: "venus"
        --wlan)        BUILD+="wlan ";OPTS="true";;     # build name: "wlan"
        --cpucp)       BUILD+="cpucp ";OPTS="true";;    # build name: "cpucp"
        --btfw)        BUILD+="btfw_mp ";OPTS="true";;     # build name: "btfw_mp"
        --wlan_hsp)    BUILD+="wlan_hsp ";OPTS="true";;   # build name: "wlan_hsp"
        --)            break;;
    esac
    shift

    if [ "$1" = "" ]; then
        break
    fi
done

if [ "$OPTS" != "true" ]; then
    usage
    exit 1
fi

if [ ! -n "$meta_dir" ]; then
    meta_dir=`pwd`
fi

if [[ `basename $meta_dir` != "BP-CODE" ]] &&  [[ ! -d "$meta_dir/BP-CODE" ]]; then
    echo "`basename $0` should be excuted in BP-CODE or its parent dir!"
    exit 1
fi

if [ "$(type -t set_log)" = "function" ] ; then
    set_log $BUILD_LOG $LOGFILE
else
    if [ "$BUILD_LOG" = "true" ]; then
        log_file=$LOGFILE
    else
        log_file=/dev/null
    fi
fi

# Get build list in this platform, such as
# "common", "modem", "apps", "tz", "boot"...
build_list=`python common/build/app/meta_cli.py get_build_list | xargs | \
    sed -e 's/ //g' | sed "s/\[//;s/\]//"`
build_arr=(${build_list//,/ })

if [[ "$ALL" = "true" ]]; then
    BUILD=${build_arr[@]}
fi

for subsys in ${BUILD[@]}
do
    # Skipe to dealing with apps(LINUX/android)
    if [[ "$subsys" = "apps" ]]; then
        continue
    fi

    # Check if the subsystem exists in the platform
    if [[ ${build_list[@]/$subsys/} == ${build_arr[@]} ]]; then
        echo "Error: No $subsys in this platform"
        exit 1
    fi

    # Get the file list in the subsystem
    file_list=`python common/build/app/meta_cli.py get_files flavor=$FLAVOR storage=$STORAGE \
        build=$subsys | sed "s/\"//;s/^\[.*$/\"/;s/\].*$/\"/;s/\",//;s/\"//"`

    if [ ! -n "$file_list" ]; then
        echo "Warnning: No build files in $subsys"
        continue
    fi

    # Get the root name of the subsystem, such as "adsp_proc" for subsystem "adsp"
    base=`basename $PWD`
    subsys_path=`echo ${file_list[0]} | sed "s/.*$base\///;s/\/.*//"`

    update_bin_files ${subsys_path} "${file_list[*]}"

done

cd $meta_dir
