#!/bin/bash
# Author: <PERSON> <<EMAIL>>
# Date: 2025-03-22
# Copyright (c) 2025, Thundercomm All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
#       copyright notice, this list of conditions and the following
#       disclaimer in the documentation and/or other materials provided
#       with the distribution.
#     * Neither the name of The Linux Foundation nor the names of its
#       contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
# ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
# BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
# BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
# WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
# OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
# IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

usage() {
cat <<USAGE

Usage:
    bash $0 [OPTIONS] [OPTIONS]

Description:
    package ota images.

OPTIONS:
    -h, --help       Display this help message
    -p, --product <product name>  set product name.
    -v, --variant <variant name>  set variant name.
    -o, --old <source>.
    -n, --new <dist>.   
    --ota-nonhlos, --nonhlos       generate full ota_package.

Example:
    package full ota package:
        $0 --ota-nonhlos

    package incremental ota package:
        $0 -o /path/to/old/version.zip -n /path/to/new/version.zip
USAGE
}

assert() {
    if [ $? -ne 0 ]; then
        exit 1
    fi
}

check_if_file_exists () {
    if [[ ! -f "$1" ]]; then
        echo "Could not find the file: \"$1\", aborting.." 2>&1 | tee -a $LOG_FILE
        exit 1
    fi
}

declare -A PARTITION_IMAGE_MAPPING=(
    ["xbl"]="boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/xbl.elf"
    ["xbl_config"]="boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/xbl_config.elf"
    ["aop"]="aop_proc/build/ms/bin/AAAAANAZO/kodiak/aop.mbn"
    ["tz"]="trustzone_images/build/ms/bin/IAGAANAA/tz.mbn"
    ["hyp"]="trustzone_images/build/ms/bin/IAGAANAA/hypvm.mbn"
    ["modem"]="common/build/ufs/bin/asic/NON-HLOS.bin"
    ["bluetooth"]="common/build/ufs/bin/BTFM.bin"
    ["abl"]="LINUX/android/out/target/product/lahaina/abl.elf"
    ["dsp"]="common/build/bin/dspso.bin"
    ["keymaster"]="qtee_tas/build/ms/bin/IAGAANAA/km41.mbn"
    ["uefisecapp"]="qtee_tas/build/ms/bin/IAGAANAA/uefi_sec.mbn"
    ["devcfg"]="trustzone_images/build/ms/bin/IAGAANAA/devcfg.mbn"
    ["qupfw"]="common/core_qupv3fw/kodiak/qupv3fw.elf"
    ["shrm"]="boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/shrm.elf"
    ["imagefv"]="boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/imagefv.elf"
    ["cpucp"]="cpucp_proc/kodiak/cpucp/cpucp.elf"
    ["featenabler"]="qtee_tas/build/ms/bin/IAGAANAA/featenabler.mbn"
    ["qweslicstore"]="LINUX/android/out/target/product/lahaina/qweslicstore.bin"
    ["multiimgoem"]="common/build/bin/multi_image.mbn"
)

declare -A PARTITION_IMAGE_MAPPING_AP=(
    ["system"]="LINUX/android/out/target/product/lahaina/system.img"
    ["vendor"]="LINUX/android/out/target/product/lahaina/vendor.img"
    ["boot"]="LINUX/android/out/target/product/lahaina/boot.img"
    ["dtbo"]="LINUX/android/out/target/product/lahaina/dtbo.img"
    ["vbmeta"]="LINUX/android/out/target/product/lahaina/vbmeta.img"
)

# 1. need check IMAGES AP images
# 2. need check META/ab_partitions.txt and RADIO BP images
add_bp_images() {
    cd $DIST_DIR
    if [ -d "META" ]; then
        echo "rm -fr META" 2>&1 | tee -a $LOG_FILE
        rm -fr META
    fi
    unzip $TARGET_FILES_ZIP META/ab_partitions.txt

    if [ -d "RADIO" ]; then
        echo "rm -fr RADIO" 2>&1 | tee -a $LOG_FILE
        rm -fr RADIO
    fi
    mkdir RADIO

    cd $meta_dir
    RADIO_PATH="$DIST_DIR/RADIO"
    for partition in "${!PARTITION_IMAGE_MAPPING[@]}"; 
    do
        if ! grep -q -E "\<$partition\>" $DIST_DIR/META/ab_partitions.txt; then
            echo "add $partition to META/ab_partitions.txt" 2>&1 | tee -a "$LOG_FILE"
            echo -e "$partition" >> "$DIST_DIR/META/ab_partitions.txt"
        fi
        image="${PARTITION_IMAGE_MAPPING[$partition]}"
        if [ -e "$image" ]; then
            echo "cp $image $RADIO_PATH/${partition}.img" 2>&1 | tee -a "$LOG_FILE"
            cp "$image" "$RADIO_PATH/${partition}.img"
            assert
        fi
    done

    cd $DIST_DIR
    zip $TARGET_FILES_ZIP -u META/ab_partitions.txt
    zip $TARGET_FILES_ZIP -m RADIO/*

    cp $TARGET_FILES_ZIP $OUTPUT_DIR/
}

generate_full_ota_zip () {
    cd $ANDROID_PATH

    if [ $VARIANT = "user" ] ; then
        SELECT_KEY=$RELEASE_KEY
    else
        SELECT_KEY=$TEST_KEY
    fi

    FULL_OTA_CMD="./$OTA_GEN_PY -v --block -p out/host/linux-x86/ -k $SELECT_KEY $TARGET_FILES_PATH full_update.zip"
    echo "run full ota cmd: $FULL_OTA_CMD" 2>&1 | tee -a $LOG_FILE
    $FULL_OTA_CMD
    assert

    # rename and copy the full ota package
    BASE_NAME=OTA_FULL_$TURBOX_ID
    FULL_OTA_PACKAGE=$OUTPUT_DIR/$BASE_NAME.zip
    echo "cp full_update.zip $FULL_OTA_PACKAGE" 2>&1 | tee -a $LOG_FILE
    cp full_update.zip $FULL_OTA_PACKAGE
    assert
}

generate_incremental_ota_zip () {
    cd $ANDROID_PATH

    if [ -d "SOURCEZIP_DIR" ]; then
        echo "rm -fr SOURCEZIP_DIR" 2>&1 | tee -a $LOG_FILE
        rm -fr SOURCEZIP_DIR
    fi
    if [ -d "DISTZIP_DIR" ]; then
        echo "rm -fr DISTZIP_DIR" 2>&1 | tee -a $LOG_FILE
        rm -fr DISTZIP_DIR
    fi
    unzip $SOURCEZIP SYSTEM/build.prop -d SOURCEZIP_DIR
    unzip $DISTZIP SYSTEM/build.prop -d DISTZIP_DIR

    SOURCE_BUILD_VERSION=$(grep "ro.system.build.version.incremental=" SOURCEZIP_DIR/SYSTEM/build.prop | sed 's/\.//g')
    SOURCE_BUILD_VERSION=${SOURCE_BUILD_VERSION##*=}
    DIST_BUILD_VERSION=$(grep "ro.system.build.version.incremental=" DISTZIP_DIR/SYSTEM/build.prop | sed 's/\.//g')
    DIST_BUILD_VERSION=${DIST_BUILD_VERSION##*=}

    if [ "$SOURCE_BUILD_VERSION" -lt "$DIST_BUILD_VERSION" ]; then
        echo "$SOURCE_BUILD_VERSION < $DIST_BUILD_VERSION, generate upgrade package" 2>&1 | tee -a $LOG_FILE
        OPTIONS="upgrade"
    elif [ "$SOURCE_BUILD_VERSION" -gt "$DIST_BUILD_VERSION" ]; then
        echo "$SOURCE_BUILD_VERSION > $DIST_BUILD_VERSION, generate downgrade package" 2>&1 | tee -a $LOG_FILE
        OPTIONS="downgrade"
    else
        echo "$SOURCE_BUILD_VERSION = $DIST_BUILD_VERSION, generate upgrade package" 2>&1 | tee -a $LOG_FILE
        OPTIONS="upgrade"
    fi

    if [ $VARIANT = "user" ] ; then
        SELECT_KEY=$RELEASE_KEY
    else
        SELECT_KEY=$TEST_KEY
    fi

    INCROTACMD_UP="./$OTA_GEN_PY -v --block -p out/host/linux-x86/ -k $SELECT_KEY -i $SOURCEZIP $DISTZIP incr_update.zip"
    INCROTACMD_DOWN="./$OTA_GEN_PY -v --block --downgrade -p out/host/linux-x86/ -k $SELECT_KEY -i $SOURCEZIP $DISTZIP incr_update.zip"

    if [ $OPTIONS = "downgrade" ] ; then
        echo "downgrade run cmd: $INCROTACMD_DOWN" 2>&1 | tee -a $LOG_FILE
        $INCROTACMD_DOWN
    elif [ $OPTIONS = "upgrade" ] ; then
        echo "upgrade run cmd: $INCROTACMD_UP" 2>&1 | tee -a $LOG_FILE
        $INCROTACMD_UP
    else
        echo "error:OPTIONS=$OPTIONS" 2>&1 | tee -a $LOG_FILE
        usage
        exit 1
    fi

    # rename and copy the incremental ota package
    BASE_NAME1=OTA_INCRMENTAL_$TURBOX_ID

    INCR_OTA_PACKAGE=$OUTPUT_DIR/$BASE_NAME1.zip
    echo "cp incr_update.zip $INCR_OTA_PACKAGE" 2>&1 | tee -a $LOG_FILE
    cp incr_update.zip $INCR_OTA_PACKAGE
    assert
}

#===========================================================
while true;
do
    case "$1" in
        -h|--help)          usage; exit 0;;
        -p|--product)       PRODUCT=$2;;
        -v|--variant)       VARIANT=$2;;
        -o|--old)           OPTIONS="incremental"; SOURCEZIP="$2";;
        -n|--new)           DISTZIP="$2";;
        --ota-nonhlos|--nonhlos)          OPTIONS="full";;
        --)                 break;;
    esac
    shift

    if [ "$1" = "" ]; then
        break
    fi
done

if [ "$PRODUCT" = "" ]; then
    PRODUCT="lahaina"
fi

if [ "$VARIANT" = "" ]; then
    VARIANT="userdebug"
fi

if [ ! -n "$meta_dir" ]; then
    meta_dir=`pwd`
fi

if [ ! -n "$ProjectName" ]; then
    ProjectName="NA"
fi
if [ ! -n "$HardWareVersion" ]; then
    HardWareVersion="xx.xx"
fi
if [ ! -n "$BaseLine" ]; then
    BaseLine="NA"
fi
if [ ! -n "$BuildType" ]; then
    BuildType="l"
fi
if [ ! -n "$BuildVariant" ]; then
    BuildVariant="userdebug"
fi
if [ ! -n "$MajorVersion" ]; then
    MajorVersion=`date +%Y%m%d`
fi
if [ ! -n "$SubVersion" ]; then
    SubVersion=`date +%H%M%S`
fi

if [ ! -n "$SoftWareVersion" ]; then
        SoftWareVersion=${BaseLine}.${BuildType}.${BuildVariant}.${MajorVersion}.${SubVersion}
fi

if [ ! -n "$TURBOX_ID" ]; then
    TURBOX_ID="${ProjectName}_${HardWareVersion}_${SoftWareVersion}"
fi


LOG_FILE="$meta_dir/zip_ota_package-`date +%Y-%m-%d_%H-%M-%S`.log"

ANDROID_PATH="$meta_dir/QCM6490_apps_qssi13/LINUX/android/"
DIST_DIR="$meta_dir/LINUX/android/out/dist"
OUTPUT_DIR="$meta_dir/turbox/output"

TARGET_FILES_ZIP="merged-qssi_${PRODUCT}-target_files.zip"
TARGET_FILES_PATH="$DIST_DIR/$TARGET_FILES_ZIP"

TEST_KEY="build/target/product/security/testkey"
RELEASE_KEY="build/target/product/security/testkey"

OTA_GEN_PY="out/host/linux-x86/bin/ota_from_target_files"

# export jdk11 for jenkins build
export JAVA_HOME=/usr/lib/jvm/jdk-11.0.14
export CLASSPATH=.:$JAVA_HOME/lib
export PATH=$JAVA_HOME/bin:$PATH

if [ "$OPTIONS" = "full" ]; then
    check_if_file_exists "$TARGET_FILES_PATH"
    add_bp_images
    generate_full_ota_zip
    echo "generate_full_ota_zip done !" 2>&1 | tee -a $LOG_FILE
elif [ "$OPTIONS" = "incremental" ]; then
    if [ -e "$SOURCEZIP" ] && [ -e "$DISTZIP" ]; then
        generate_incremental_ota_zip
        echo "generate_incremental_ota_zip done !" 2>&1 | tee -a $LOG_FILE
    else
        check_if_file_exists "$SOURCEZIP"
        check_if_file_exists "$DISTZIP"
    fi
else
    usage
fi
