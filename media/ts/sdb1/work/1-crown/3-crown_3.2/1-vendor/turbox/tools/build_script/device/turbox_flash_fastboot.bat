@echo off
rem Author: <PERSON><PERSON><PERSON> Cong <<EMAIL>>
rem Date: 2021-08-09
rem Copyright (c) 2021, Thundercomm All rights reserved.
rem
rem Redistribution and use in source and binary forms, with or without
rem modification, are permitted provided that the following conditions are
rem met:
rem     * Redistributions of source code must retain the above copyright
rem       notice, this list of conditions and the following disclaimer.
rem     * Redistributions in binary form must reproduce the above
rem       copyright notice, this list of conditions and the following
rem       disclaimer in the documentation and/or other materials provided
rem       with the distribution.
rem     * Neither the name of The Linux Foundation nor the names of its
rem       contributors may be used to endorse or promote products derived
rem       from this software without specific prior written permission.
rem
rem THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
rem WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
rem MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
rem ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
rem BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
rem CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
rem SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
rem BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
rem WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
rem OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
rem IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
rem 
set FORCE="false"
set REBOOT="false"
set HLOS="false"
set NONHLOS="false"
set ALL="false"
set STOP="false"
set PRODUCT="c6490"
set VARIANT="debug"
rem Program entrance
if "%1" == "" (
    call :flash_hlos
    echo "Loading complete, Device rebooting ..."
    fastboot reboot
    goto :eof
) else (
    call:CheckOpts %*
    goto:eof
)
goto:eof
:CheckOpts
    if "%1" == "-h" (
        call:usage
    ) else if "%1" == "-f" (
        set FORCE="true"
        shift
        goto CheckOpts %*
    ) else if "%1" == "-r" (
        set REBOOT="true"
        shift
        goto CheckOpts %*
    ) else if "%1" == "-a" (
        set HLOS="true"
        shift
        goto CheckOpts %*
    ) else if "%1" == "-b" (
        set NONHLOS="true"
        shift
        goto CheckOpts %*
    ) else if "%1" == "--all" (
        set ALL="true"
        shift
        goto CheckOpts %*
    ) else if "%1" == "-s" (
        set STOP="true"
        shift
        goto CheckOpts %*
    ) else if "%1" == "" (
        call:flash_devices
        goto :eof
    ) else (
        echo "\nERROR: Getopt failed. Extra args\n"
        call :usage
        goto:eof
    )
    goto:eof
:usage
    echo "Usage:"
    echo "  bash $0 [options] [options]"
    echo "  The images except gpt,userdata,persist will be flashed if no options input"
    echo "Description:"
    echo "  flash images with fasboot"
    echo "Options:"
    echo "  -h, --help   display this help message"
    echo "  -v, --variant <debug/perf/user>    set the build variant.(default: debug)"
    echo "  -f, --force  forcibly flash gpt,persist and userdata without confirmation."
    echo "\033[31m               User data and calibration data(BT/WIFI MAC address, etc..) will be erased. "
    echo "  -r, --reboot      reboot the device automatically after flashing completed."
    echo "  -b, --nonhlos     flash all bp images(xbl,rpm,ftfm,tz,dsp,modem,pmic)."
    echo "  -a, --hlos        flash all ap images(aboot,bootimage,recovery,system,vendor,cache,mdtp,userdata,persist)."
    echo "  --all        flash all images"
    echo "  -s, --stop   default no pause"
    echo
    echo "Example:"
    echo "  ./turbox_flash.sh"
    echo "      Flash the images except gpt,userdata,persist."
    echo "  ./turbox_flash.sh -b"
    echo "      Flash all images."
    echo "  ./turbox_flash.sh -a"
    echo "      Flash all ap images."
    echo
    if %STOP% == "false" (
        pause
    )
    goto:eof
:flash_devices
    if %ALL% == "true" (
        call :flash_gpt
        call :flash_nonhlos
        call :flash_hlos
        call :flash_persist
        call :flash_data
    )else (
        if %HLOS% == "true" (
            call :flash_hlos
            call :flash_persist
            call :flash_data
        )
        if %NONHLOS% == "true" (
            call :flash_nonhlos
        )
    )
    if %REBOOT% == "true" (
        echo "Loading complete, Device rebooting ..."
        fastboot reboot
    ) else (
        echo "Loading complete, please reboot the device"
    )
    if %STOP% == "false" (
        pause
     )
    goto:eof
:flash_hlos
    fastboot flash abl_a LINUX/android/out/target/product/lahaina/abl.elf
    fastboot flash boot_a LINUX/android/out/target/product/lahaina/boot.img
    fastboot flash dtbo_a LINUX/android/out/target/product/lahaina/dtbo.img
    fastboot flash metadata LINUX/android/out/target/product/lahaina/metadata.img
    fastboot flash super LINUX/android/out/target/product/lahaina/super.img
    fastboot flash vbmeta_a LINUX/android/out/target/product/lahaina/vbmeta.img
    fastboot flash vbmeta_system_a LINUX/android/out/target/product/lahaina/vbmeta_system.img
    fastboot flash vendor_boot_a LINUX/android/out/target/product/lahaina/vendor_boot.img
    goto:eof
:flash_nonhlos
    fastboot flash modem_a common/build/ufs/bin/asic/NON-HLOS.bin
    fastboot flash bluetooth_a common/build/ufs/bin/BTFM.bin
    fastboot flash dsp_a common/build/bin/dspso.bin
    fastboot flash splash common/build/bin/splash.img
    fastboot flash qupfw_a common/core_qupv3fw/kodiak/qupv3fw.elf
    fastboot flash devcfg_a trustzone_images/build/ms/bin/IAGAANAA/devcfg.mbn
    fastboot flash hyp_a trustzone_images/build/ms/bin/IAGAANAA/hypvm.mbn
    fastboot flash keymaster_a qtee_tas/build/ms/bin/IAGAANAA/km41.mbn
    fastboot flash featenabler_a qtee_tas/build/ms/bin/IAGAANAA/featenabler.mbn
    fastboot flash storsec qtee_tas/build/ms/bin/IAGAANAA/storsec.mbn
    fastboot flash uefisecapp_a qtee_tas/build/ms/bin/IAGAANAA/uefi_sec.mbn
    fastboot flash imagefv_a boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/imagefv.elf
    fastboot flash logfs boot_images/boot/QcomPkg/Tools/binaries/logfs_ufs_8mb.bin
    fastboot flash tz_a trustzone_images/build/ms/bin/IAGAANAA/tz.mbn
    fastboot flash xbl_a boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/xbl.elf
    fastboot flash xbl_config_a boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/xbl_config.elf
    fastboot flash rtice trustzone_images/build/ms/bin/IAGAANAA/rtice.mbn
    fastboot flash shrm_a boot_images/boot/QcomPkg/SocPkg/Kodiak/Bin/LAA/RELEASE/shrm.elf
    fastboot flash secdata common/sectools/resources/build/sec.dat
    fastboot flash aop_a aop_proc/build/ms/bin/AAAAANAZO/kodiak/aop.mbn
    goto:eof
:flash_variable
    echo "User data and the calibration data(BT/WIFI MAC address, etc..) will be ereased. Are you sure flash the gpt.(Y/yes)?"
    set /p variable=please input:
    echo %variable% | findstr "Y" > nul
    if %errorlevel% equ 0 (
        fastboot flash partition:0 QCM2290.LA.1.0.1/common/build/emmc/gpt_both0.bin
    ) else (
        echo Flash gpt error!!!
    )
    goto:eof
:flash_gpt
    if %FORCE% == "true" (
        fastboot flash partition:0 QCM2290.LA.1.0.1/common/build/emmc/gpt_both0.bin
    ) else (
        call :flash_variable
    )
    goto:eof
:flash_variable_persist
    echo "Calibration data(BT/WIFI MAC address, etc..) will be ereased. Are you sure to flash the persist image(Y/yes)?"
    set /p variable_persist=please input:
    echo %variable_persist% | findstr "Y" > nul
    if %errorlevel% equ 0 (
        fastboot flash persist LINUX/android/out/target/product/lahaina/persist.img
    ) else (
        echo Flash Persist error!!!
    )
    goto:eof
:flash_persist
    if %FORCE% == "true" (
        fastboot flash persist LINUX/android/out/target/product/lahaina/persist.img
    ) else (
        call :flash_variable_persist
    )
    goto:eof
:flash_variable_data
    echo test3
    echo "User data will be ereased. Are you sure to flash the data image(Y/yes)?"
    set /p variable_data=please input:
    echo %variable_data% | findstr "Y" > nul
    if %errorleve2% equ 0 (
        fastboot flash userdata LINUX/android/out/target/product/lahaina/userdata.img
    ) else (
        echo Flash Super error!!!
    )
    goto:eof
:flash_data
    if %FORCE% == "true" (
        fastboot flash userdata LINUX/android/out/target/product/lahaina/userdata.img
    ) else (
        call:flash_variable_data
    )
    goto:eof
