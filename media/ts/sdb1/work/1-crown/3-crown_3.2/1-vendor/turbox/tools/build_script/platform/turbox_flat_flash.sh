#!/bin/bash
# <AUTHOR> <EMAIL>
# Date: 2020-10-23
# Copyright (c) 2020, Thundercomm All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
#       copyright notice, this list of conditions and the following
#       disclaimer in the documentation and/or other materials provided
#       with the distribution.
#     * Neither the name of The Linux Foundation nor the names of its
#       contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
# ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
# BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
# BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
# WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
# OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
# IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
usage() {
    echo -e "\033[1;37mUsage:\033[0m"
    echo -e "  bash $0 [options] [options]"
    echo -e "\033[1;37m   if no options input\033[0m"
    echo

    echo -e "\033[1;37mDescription:\033[0m"
    echo -e "  Use fh_loader to flash Flatbuild ROM."
    echo

    echo -e "\033[1;37mOptions:\033[0m"
    echo -e "\033[1;37m  -h, --help\033[0m        display this help message"
    echo -e "\033[1;37m  -p, --port </dev/ttyUSB*>\033[0m      specify the usb port(/dev/ttyUSB*)"

    echo -e "\033[1;37mExample:\033[0m"
    echo -e "\033[1;37m  ./turbox_flat_flash.sh\033[0m"
    echo -e "      Flash Flatbuild ROM with fh_loader and the port is default(/dev/ttyUSB0)."
    echo
    echo -e "\033[1;37m  ./turbox_flash.sh  -p /dev/ttyUSB0"
    echo -e "      Flash Flatbuild ROM with fh_loader and the port is /dev/ttyUSB0."
    echo

}

# sudo turbox/tools/flash_tools/fh_loader/QSaharaServer -p /dev/ttyUSB0 -s 13:/home/<USER>/tmp/flatbuild/emmc/prog_firehose_ddr.elf
# sudo turbox/tools/flash_tools/fh_loader/fh_loader
#        --port=/dev/ttyUSB1
#        --sendxml=rawprogram_unsparse0.xml,patch0.xml
#        --search_path=/home/<USER>/tmp/flatbuild/emmc
#        --noprompt
#        --showpercentagecomplete
#        --memoryname=eMMC

if [ ! -n "$FLAT_DIR" ]; then
    FLAT_DIR=`pwd`
fi

assert() {
    if [ $? -ne 0 ];then
        echo "`date +%Y%m%d_%H:%M:%S` [TURBOX BUILD ERROR]: $1 Error****" | tee -a $log_file
        exit 1;
    fi
}

# ==================================================================
QSAHARASERVER=$FLAT_DIR/QSaharaServer
FHLOADER=$FLAT_DIR/fh_loader
FH_LOAD_ARGS="--sendxml=rawprogram_unsparse0.xml,patch0.xml --noprompt --showpercentagecomplete"
MEM=eMMC
flash_fh_loader() {
    echo " sudo $QSAHARASERVER -p $PORT -s 13:$FLAT_DIR/emmc/prog_firehose_ddr.elf"
    sudo $QSAHARASERVER -p $PORT -s 13:$FLAT_DIR/emmc/prog_firehose_ddr.elf
    assert

    echo "sudo $FH_LOADER $FH_LOAD_ARGS --port=$PORT --search_path=$FLAT_DIR/emmc --memoryname=$MEM"
    sudo $FHLOADER $FH_LOAD_ARGS --port=$PORT --search_path=$FLAT_DIR/emmc --memoryname=$MEM
    assert
}
# ======================Setup getopt================================
long_opts="help,port:"
getopt_cmd=$(getopt -o hfrabv:ld:p:m: --long "$long_opts" \
           -n $(basename $0) -- "$@") || \
           { echo -e "\nERROR: Getopt failed. Extra args\n"; usage; exit 1;}

eval set -- "$getopt_cmd"

PORT="/dev/ttyUSB0"

while true; do
    case "$1" in
        -h|--help)       usage; exit 0;;
        -p|--port)       PORT="$2";shift;;
    esac
    shift

    if [ "$1" = "" ]; then
        break
    fi
done
flash_fh_loader
