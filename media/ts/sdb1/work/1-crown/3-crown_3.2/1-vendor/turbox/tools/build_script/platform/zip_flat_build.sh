#!/bin/bash
# Author: <PERSON><PERSON> <<EMAIL>>
# Date: 2020-02-03
# Copyright (c) 2020, Thundercomm All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
#       copyright notice, this list of conditions and the following
#       disclaimer in the documentation and/or other materials provided
#       with the distribution.
#     * Neither the name of The Linux Foundation nor the names of its
#       contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
# ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
# BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
# BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
# WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
# OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
# IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

LOGFILE=zip_flat_build.log

usage() {
cat <<USAGE

Usage:
    bash $0 [OPTIONS] [OPTIONS]

Description:
    package flat build files.

OPTIONS:
    -l, --log               Put build log to file
    -h, --help              Display this help message
    -x, --contentsxml       Absolute path of contents.xml
    -m, --memoryname        Memory name

Example:
    package flat build files:
        $0

    package flat build files and show log:
        $0 -l
USAGE
}

if [ ! -n "$flat_dir" ]; then
    flat_dir=`pwd`
fi

TARGETDIR=$flat_dir/turbox/output
TARGETFILE=FlatBuild_$TURBOX_ID

FH_LOADER=$flat_dir/turbox/tools/flash_tools/fh_loader/fh_loader
FH_LOADER_ARGS="--flavor=asic --noprompt --showpercentagecomplete"

#CURR_DIR=$(cd "$(dirname "$0")"; pwd)
CONTENTSXML=${flat_dir}/contents.xml

MEMORYNAME=ufs

# ==================================================================
assert() {
    if [ $? -ne 0 ];then
        exit 1;
    fi
}

# Original command:
#	sudo ~/bin/fh_loader --contentsxml=/home/<USER>/Source/CM6125/contents.xml
#			--flavor=asic
#			--flattenbuildto=/home/<USER>/tmp/flatbuild
#			--noprompt
#			--showpercentagecomplete
#			--memoryname=emmc
copy_files() {
    if [ -d $TARGETDIR/$TARGETFILE ]; then
        echo "rm -fr $TARGETDIR/$TARGETFILE" 2>&1 | tee -a $log_file
        rm -fr $TARGETDIR/$TARGETFILE
    fi

    echo "mkdir -p $TARGETDIR/$TARGETFILE" 2>&1 | tee -a $log_file
    mkdir -p $TARGETDIR/$TARGETFILE

    cd $flat_dir

    $FH_LOADER $FH_LOADER_ARGS --contentsxml=$CONTENTSXML --memoryname=$MEMORYNAME --flattenbuildto=$TARGETDIR/$TARGETFILE
    assert
}

zip_package() {
    cd $TARGETDIR

    echo "zip -r $TARGETFILE.zip $TARGETFILE" 2>&1 | tee -a $log_file
    zip -r $TARGETFILE.zip $TARGETFILE
    assert
}

# ======================Setup getopt================================
while true; do
    case "$1" in
      -l|--log)               BUILD_LOG="true";OPTS="true";;
      -h|--help)              usage; exit 0;;
#      -x|--contentsxml)       CONTENTSXML=$2;;
#      -m|--memoryname)        MEMORYNAME=$2;;
      --)         break;;
    esac
    shift

    if [ "$1" = "" ]; then
        break
    fi
done

if [ "$(type -t set_log)" = "function" ] ; then
    set_log $BUILD_LOG $LOGFILE
else
    if [ "$BUILD_LOG" = "true" ]; then
        log_file=$LOGFILE
    else
        log_file=/dev/null
    fi
fi

copy_files
zip_package

cd $meta_dir
