#!/bin/bash
#Author: jiaqi.zheng <<EMAIL>>
# Date: 2020-12-28
# Copyright (c) 2020, Thundercomm All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
#       copyright notice, this list of conditions and the following
#       disclaimer in the documentation and/or other materials provided
#       with the distribution.
#     * Neither the name of The Linux Foundation nor the names of its
#       contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
# ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
# BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
# BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
# WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
# OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
# IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

LOGFILE=build_adsp.log

usage() {
cat <<USAGE

Usage:
    bash $0 [OPTIONS] [OPTIONS]

Description:
    Builds adsp_proc

OPTIONS:
    -l, --log        Put build log to file
    -c, --clean      Claen the build
    -h, --help       Display this help message

Example:
    build adsp_proc:
        $0

    clean build:
        $0 -c

    build adsp_proc and save build log:
        $0 -l

    clean build and save build log:
        $0 -c -l
USAGE
}

ARMTOOLPATH=/pkg/qct/software/arm/RVDS/5.05bld106
HEXAGONTOOLPATH=/pkg/qct/software/hexagon/releases/tools
LLVMROOT_PATH=/pkg/qct/software/llvm/release/arm/*******
GNUROOT_PATH=/pkg/qct/software/arm/linaro-toolchain/aarch64-none-elf/4.9-2014.07
GNUARM7_PATH=/pkg/qct/software/arm/linaro-toolchain/aarch64-none-elf/arm-linux-gnueabihf-4.8-2014.02


export ARMTOOLS=RVCT221 
export ARMROOT=/pkg/asw/compilers/arm/ADS1.2
export ARM_COMPILER_PATH=/pkg/qct/software/arm/RVDS/2.2BLD593/RVCT/Programs/2.2/593/linux-pentium
export HEXAGON_ROOT=/pkg/qct/software/hexagon/releases/tools
export GNUROOT=/pkg/qct/software/arm/linaro-toolchain/aarch64-none-elf/4.9-2014.07
export GNUARM7=/prj/llvm-arm/home/<USER>/build_tools/gcc-linaro-arm-linux-gnueabihf-4.8-2014.02_linux
export SECTOOLS_DIR=$meta_dir/common/sectools

SRC_PATH="adsp_proc/build/ms"
BUILD_CMD1="python ./build_variant.py kodiak.adsp.prod"
#BUILD_CMD2="python  ./build/build.py -c agatti -f ADSP,USES_ENABLE_SKU -o all"
#CLEAN_CMD2="python ./build/build.py -c agatti -f ADSP,USES_ENABLE_SKU -o clean"
CLEAN_CMD1="python ./build_variant.py kodiak.adsp.prod -clean"
# ==================================================================
assert() {
    if [ ${PIPESTATUS[0]} -ne 0 ]; then
        exit 1;
    fi
}

set_env_adsp()
{
    export ARMLMD_LICENSE_FILE=8224@localhost
    ARM_COMPILER_PATH=/pkg/qct/software/arm/ADS1.2/bin
    PYTHON_PATH=/pkg/qct/software/python/2.7.5/bin MAKE_PATH=/pkg/gnu/make/3.81/bin
    export ARMTOOLS=RVCT221
    export ARMROOT=/pkg/asw/compilers/arm/ADS1.2
    export ARMLIB=/pkg/asw/compilers/arm/ADS1.2/lib
    export ARMINCLUDE=/pkg/asw/compilers/arm/ADS1.2/common/include
    export ARMINC=/pkg/asw/compilers/arm/ADS1.2/common/include
    export ARMCONF=/pkg/asw/compilers/arm/ADS1.2/bin
    export ARMDLL = /pkg/asw/compilers/arm/ADS1.2/bin
    export PATH=$MAKE_PATH:$PYTHON_PATH:$ARM_COMPILER_PATH:$PATH
    export ARMHOME = /pkg/asw/compilers/arm/ADS1.2
    export ARMROOT = /pkg/asw/compilers/arm/ADS1.2
    export HAVANA_TOOLCHAIN = /pkg/qct/software/arm/Sourcery_G++_Lite/bin
    export HEXAGON_ROOT=/pkg/qct/software/hexagon/releases/tools
}
build_adsp() {
    cd $SRC_PATH

    if [ "$BUILD_CLEAN" = "true" ]; then
         echo "$BUILD_CLEAN1" 2>&1 | tee -a $log_file
         $CLEAN_CMD1 2>&1 | tee -a $log_file
         assert
         #echo "$BUILD_CLEAN2" 2>&1 | tee -a $log_file
         #$CLEAN_CMD2 2>&1 | tee -a $log_file
	 #assert
    else
	echo "$BUILD_CMD1" 2>&1 | tee -a $log_file
        $BUILD_CMD1 2>&1 | tee -a $log_file
        assert
	#echo "$BUILD_CMD2" 2>&1 | tee -a $log_file
        #$BUILD_CMD2 2>&1 | tee -a $log_file
        tail -n 1 $log_file | grep -c "FAILED"
        if [  $? -ne 0  ];then
            echo "sucessful"
        else
	    exit 1;
        fi
    fi

    #set_env_adsp

    assert
}

# ======================Setup getopt================================
BUILD_CLEAN="false"
while true; do
    case "$1" in
        -c|--clean)   BUILD_CLEAN="true";OPTS="true";;
        -l|--log)     BUILD_LOG="true";OPTS="true";;
        -h|--help)    usage; exit 0;;
        --)           break;;
    esac
    shift

    if [ "$1" = "" ]; then
        break
    fi
done

if [ "$(type -t set_log)" = "function" ] ; then
    set_log $BUILD_LOG $LOGFILE
else
    if [ "$BUILD_LOG" = "true" ]; then
        log_file=$LOGFILE
    else
        log_file=/dev/null
    fi
fi

if [ ! -n "$meta_dir" ]; then
    meta_dir=`pwd`
fi

build_adsp
cd $meta_dir
