Author: <PERSON><PERSON><PERSON> Cong <<EMAIL>>
Date: 2021-09-01
Copyright (c) 2021, Thundercomm All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of The Linux Foundation nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERC<PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR
<PERSON>NSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
/*********************************************************************/
/*********************************************************************/
The current compression package is for flash the Turbox Board using flat.
In Linux,turbox_flash_flat.c should be used.

------------------------------------------------------------------------------
Example Usage of the scripts
------------------------------------------------------------------------------

Step 1. Enter the 9008 mode

        a. Power off the thedevice by disconnecting the power cable and
           USB cable.
        b. Connect the power cable.
        c. Hold the USB Bootbutton.
        d. Press the Power button and wait for about 3 seconds.
        e. Release the USB Bootbutton and Power button.
        f. Connect the Type-C USB cable to an Ubuntu computer.

    NOTE: To check if your device has entered the 9008 Mode, run the 
    following command:
        $ lsusb
        ......
        Bus 001 Device 042: ID 05c6:9008 Qualcomm, Inc. Gobi Wireless Modem
        (QDL mode)
        ......

Step 2. Flash images
        $ cd <folder name of image package>
        $ sudo ./turbox_flash_flat -x emmc/patch0.xml,emmc/
          rawprogram_unsparse0.xml -f ./emmc/prog_firehose_ddr.elf
          -i emmc -s eMMC -r
        $ adb devices
          List of devices attac
          b237534        device

