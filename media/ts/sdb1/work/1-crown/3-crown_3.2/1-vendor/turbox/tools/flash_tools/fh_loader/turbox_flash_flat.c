/*
Author: <PERSON><PERSON><PERSON> Cong <<EMAIL>>
Date: 2021-08-23
Copyright (c) 2021, Thundercomm All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
 * Redistributions of source code must retain the above copyright
 notice, this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above
 copyright notice, this list of conditions and the following
 disclaimer in the documentation and/or other materials provided
 with the distribution.
 * Neither the name of The Linux Foundation nor the names of its
 contributors may be used to endorse or promote products derived
 from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, E<PERSON>EMPLAR<PERSON>, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

#include<stdio.h>
#include<string.h>
#include<stdlib.h>
#include<unistd.h>
#include<getopt.h>
#include<errno.h>
#include<bits/types.h>
#include<sys/wait.h>
#include<pthread.h>
#include<stdbool.h>

#define TYPENAMELEN     16
#define XMLLEN          256
#define FIREHOSENAMELEN 256
#define FLATBUILDPATH   512
#define DEVNAMELEN      16
#define REBOOT          256
#define ERASE           256

struct options_t {
    char        version[TYPENAMELEN];
    int         port;
    char        sendXml[XMLLEN];
    char        firehoseFile[FIREHOSENAMELEN];
    char        flatBuildPath[FLATBUILDPATH];
    char        storageType[DEVNAMELEN];
    char        reboot[REBOOT];
    char        erase[ERASE];
    char        devName[DEVNAMELEN];
};

struct options_t mOptions;

void usage() {
    printf("Formula:\n");
    printf("sudo $0 <option 1> <options 2> <...>\n");
    printf("Description:");
    printf("Flash FlatBuild image to Thundercomm TurboX SOM.\n");
    printf("Options:\n");
    printf("-h, --help    display the help message content.\n");
    printf("-p, --port /dev/ttyUSB<USB port number>    \
specify the usb port(default: /dev/ttyUSB0).\n");
    printf("-i, --flat-build <FlatBuild image path>    \
specify the flat build image path(default: the current directory).\n");
    printf("-s, --storage-type <storage type>    choose the storage type: eMMC or ufs.\n");
    printf("-x, --xml <xml files>    specify the xml files.\n");
    printf("-f, --firehose-file <firehose file path>    specify the firehose file path.\n");
    printf("-r, --reboot reboot the device automatically after flashing completes.\n");
    printf("-e, --erase  Erase the information.\n");
    printf("Example(output for default options):\n");
    printf("sudo $0 -f <FlatBuild image path>/prog_firehose_ddr.elf -i <FlatBuild image path> \
-x rawprogram_unsparse0.xml,patch0.xml -s eMMC \n");
    printf("Flash the <FlatBuild image path> ROM to TruboX SOM, mPort: /dev/ttyUSB0, files: \
rawprogram_unsparse0.xml,patch0.xml, storage-type: eMMC\n");
}

int find_qdl_device(int portNum) {
    FILE *fp = NULL;
    char cmd[512];
    char *str = NULL;
    sprintf(cmd, "count=0; for sysdevpath in $(find /sys/bus/usb/devices/usb*/ -name dev | grep ttyUSB);do syspath=${sysdevpath%%/dev}; eval $(udevadm info -q property --export -p ${syspath});[[ -z ${ID_SERIAL} || ${ID_VENDOR_ID} != \"05c6\" || ${ID_MODEL_ID} != \"9008\" ]] && continue;PORT=${DEVNAME}; let count++; done; if [ ${count} -gt 1 ] || [ ${count} -eq 0 ]; then echo ${count}; else echo ${PORT}; fi");

    if ((fp = popen(cmd, "r")) == NULL) {
        printf("Check usb device internal error\n");
    } else {
        str = fgets(cmd, sizeof(cmd), fp);
        pclose(fp);
        if (NULL != str) {
            strncpy(mOptions.devName, str, (strlen(str) - 1));
            printf("Device found: %s\n",mOptions.devName);
            if (strcmp(mOptions.devName,"0") == 0) {
                printf("No device found, please connect a device.\n");
            } else if (strcmp(mOptions.devName,"/dev/ttyUSB*") != 0 ) {
                if ( atoi(mOptions.devName) > 1) {
                    printf("More than 1 device is found, please connect only 1 device.\n");
                }
            }
            return 0;
        } else {
            printf("Failed to read usb device name\n");
        }
    }
    return 1;
}

int check_options(struct options_t mOpt) {
    int rc = 0;
    if ((access(mOpt.firehoseFile,F_OK)) != 0) {
        printf("Specify the correct firehose file path \
(E.g.: -f <FlatBuild image path>/prog_firehose_ddr.elf).\n");
        rc = 1;
    }

    if (mOpt.sendXml == NULL) {
        printf("Pecify the xml file(E.g.: -x rawprogram_unsparse0.xml,patch0.xml).\n ");
        rc = 1;
    }

    if (mOpt.storageType == NULL) {
        printf("Specify the storage type.(E.g.: -s eMMC)\n");
        rc = 1;
    }

    if (rc == 1) {
        usage();
    }
    return rc;
}

int stop_ModemManager() {
    char ans[512] = "\0";
    char cmd[512] = "\0";
    char *str = NULL;
    FILE *fp = NULL;
    printf("ModemManager will be stopped, If you actually need ModemManager, you can start it \
again after the flashing is complete.\n");
    printf("ModemManager can be started by sudo systemctl start ModemManager");
    printf("Are you sure to continue ... (Y/yes)?");
    if (scanf("%s", ans) < 0)
        printf("%s: input error\n", ans);
    getchar();
    if (!strcmp(ans, "Y") || !strcmp(ans, "yes")|| !strcmp(ans, "y")) {
        sprintf(cmd, "systemctl stop ModemManager");
        if ((fp = popen(cmd, "r")) == NULL) {
            printf("Stop ModemManager error\n");
        } else {
            str = fgets(cmd, sizeof(cmd), fp);
            pclose(fp);
            printf("Stop ModemManager success!\n");
        }
    } else {
        printf("Input error\n");
    }
    return 0;
}

int main(int argc, char * argv[])
{
    int ch;
    int REBOOT_true;
    int ERASE_true;
    char qsaharaserverOption[32];
    char fh_loadersendxml[64];
    char fh_loaderport[64];
    char fh_loadersearch[64];
    char fh_loadermemoryname[64];
    char cmd[512];
    char *str = NULL;
    FILE *fp = NULL;
    mOptions.port = 0;
    strcpy(mOptions.version,"0.3.1");

    if (argc == 1) {
        usage();
        return 0;
    }

    while ((ch = getopt(argc, argv, "p:x:f:i:s:rehv")) != -1) {
        switch (ch) {
            case 'p':
                mOptions.port = atoi(optarg);
                printf("The argument of -p is %d\n\n", mOptions.port);
                break;
            case 'x':
                memset(mOptions.sendXml, 0x00, XMLLEN);
                memcpy(mOptions.sendXml, optarg, strlen(optarg));
                printf("The argument of -x is %s\n\n", mOptions.sendXml);
                break;
            case 'f':
                memset(mOptions.firehoseFile, 0x00, FIREHOSENAMELEN);
                memcpy(mOptions.firehoseFile, optarg, strlen(optarg));
                printf("The argument of -f is %s\n\n", optarg);
                break;
            case 'i':
                memset(mOptions.flatBuildPath, 0x00, FLATBUILDPATH);
                memcpy(mOptions.flatBuildPath, optarg, strlen(optarg));
                printf("The argument of -i is %s\n\n", optarg);
                break;
            case 's':
                memset(mOptions.storageType, 0x00, DEVNAMELEN);
                memcpy(mOptions.storageType, optarg, strlen(optarg));
                printf("The argument of -s is %s\n\n", optarg);
                break;
            case 'r':
                printf("reboot the device automatically after flashing completes\n");
                REBOOT_true = 1;
                break;
            case 'e':
                printf("Erase the information");
                ERASE_true = 1;
                break;
            case 'h':
                printf("HAVE option: -help\n\n");
                usage();
                return 0;
            case 'v':
                printf("version: %s\n",mOptions.version);
                return 0;
            default:
                printf("Unknown option: %c\n",(char)optopt);
                usage();
                return 0;
        }
    }

    stop_ModemManager();

    find_qdl_device(mOptions.port);

    if (check_options(mOptions) != 0) {
        usage();
        return 0;
    }

    sprintf(qsaharaserverOption, "13:%s", mOptions.firehoseFile);
    sprintf(fh_loadersendxml,"--sendxml=%s",mOptions.sendXml);
    sprintf(fh_loaderport,"--port=%s", mOptions.devName);
    sprintf(fh_loadersearch,"--search_path=%s", mOptions.flatBuildPath);
    sprintf(fh_loadermemoryname,"--memoryname=%s", mOptions.storageType);

    sprintf(cmd, "sudo ./QSaharaServer -p %s -s 13:%s",mOptions.devName,mOptions.firehoseFile);
    if ((fp = popen(cmd, "w")) == NULL) {
        printf("QsaharaServer cmd internal error\n");
        return 0;
    } else {
        str = fgets(cmd, sizeof(cmd), fp);
        pclose(fp);
        if (NULL != str) {
            if (strcmp(str, "0\n")) {
                printf("Initializing flash process failed\n\
Please follow the README and reconnect your device to PC\n");
                return 0;
            }
        }
    }

    if (ERASE_true == 1) {
        sprintf(cmd, "sudo ./fh_loader --port=%s --erase=0 --noprompt --showpercentagecomplete --zlpawarehost=1 --memoryname=%s",mOptions.devName,mOptions.storageType);
        if ((fp = popen(cmd, "w")) == NULL) {
            printf("fh_loader cmd internal error\n");
            return 0;
        } else {
            str = fgets(cmd, sizeof(cmd), fp);
            pclose(fp);
            if (NULL != str) {
                if (strcmp(str, "0\n")) {
                    printf("Initializing flash process failed\n\
Please follow the README and reconnect your device to PC\n");
                    return 0;
                }
            }
        }
    }

    sprintf(cmd, "sudo ./fh_loader --sendxml=%s --noprompt --showpercentagecomplete --port=%s --search_path=%s --memoryname=%s",mOptions.sendXml,mOptions.devName,mOptions.flatBuildPath,mOptions.storageType);
    if ((fp = popen(cmd, "w")) == NULL) {
        printf("fh_loader cmd internal error\n");
        return 0;
    } else {
        str = fgets(cmd, sizeof(cmd), fp);
        pclose(fp);
        if (NULL != str) {
            if (strcmp(str, "0\n")) {
                printf("Initializing flash process failed\n\
Please follow the README and reconnect your device to PC\n");
                return 0;
            }
        }
    }

    if (REBOOT_true == 1) {
        sprintf(cmd, "sudo ./fh_loader --port=%s --reset --noprompt --showpercentagecomplete --zlpawarehost=1 --memoryname=%s",mOptions.devName,mOptions.storageType);
        if ((fp = popen(cmd, "w")) == NULL) {
            printf("fh_loader cmd internal error\n");
            return 0;
        } else {
            str = fgets(cmd, sizeof(cmd), fp);
            pclose(fp);
            if (NULL != str) {
                if (strcmp(str, "0\n")) {
                    printf("Initializing flash process failed\n\
Please follow the README and reconnect your device to PC\n");
                    return 0;
                }
            }
        }
    }

    return 0;
}
