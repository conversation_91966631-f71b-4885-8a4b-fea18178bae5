#!/bin/bash
#################################################################################
# Author: <PERSON> <<EMAIL>>
# Copyright (c) 2020
# All Rights Reserved by Thunder Software Technology Co., Ltd and its affiliates.
# You may not use, copy, distribute, modify, transmit in any form this file
# except in compliance with THUNDERSOFT in writing by applicable law.
#################################################################################

init_platform_env() {
    local func_name="init_platform_env"
    echo "INFO: Enter $func_name()"

## project configuration information
    ProjectName="CROWN"
    BaseLine="QCM6490.LA3.2"
    HardWareVersion="V001"

## component configuration information
    PF_BL="true"
    PF_RPM="false"
    PF_TZ="true"
    PF_ADSP="true"
    PF_CDSP="true"
    PF_MPSS="false"
    PF_SLPI="false"
    PF_AOP="true"
    PF_HLOS="true"
    PF_UBF="true"
    PF_UCI="true"
    PF_ZFB="true"
    PF_ZMB="true"
    PF_ZQFB="true"
    PF_OTA="true"
}

product_category() {
    local product;
    if [[ "$2" = "c6490" || "$2" = "ct6490" ]]; then
        product="lahaina";
    else
        product="lahaina";
    fi
    echo $product
}
export -f product_category
